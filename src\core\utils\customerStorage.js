/**
 * Utility functions for managing customers with API integration and local storage fallback
 */

import customerService from '../services/customer.service';

// Local storage keys
const CUSTOMERS_STORAGE_KEY = 'pos_customers';
const SELECTED_CUSTOMER_KEY = 'pos_selected_customer';
const CUSTOMER_ORDERS_KEY = 'pos_customer_orders';
const CUSTOMERS_CACHE_KEY = 'pos_customers_cache';
const CACHE_EXPIRY_KEY = 'pos_customers_cache_expiry';

// Cache duration in milliseconds (5 minutes)
const CACHE_DURATION = 5 * 60 * 1000;

/**
 * Check if cache is valid
 * @returns {boolean} True if cache is valid
 */
const isCacheValid = () => {
  try {
    const expiry = localStorage.getItem(CACHE_EXPIRY_KEY);
    if (!expiry) return false;

    const expiryTime = parseInt(expiry, 10);
    return Date.now() < expiryTime;
  } catch (error) {
    console.error('Error checking cache validity:', error);
    return false;
  }
};

/**
 * Get customers from cache
 * @returns {Array|null} Cached customers or null if cache is invalid
 */
const getCachedCustomers = () => {
  try {
    if (!isCacheValid()) return null;

    const cached = localStorage.getItem(CUSTOMERS_CACHE_KEY);
    return cached ? JSON.parse(cached) : null;
  } catch (error) {
    console.error('Error retrieving cached customers:', error);
    return null;
  }
};

/**
 * Cache customers data
 * @param {Array} customers - Customers to cache
 */
const cacheCustomers = (customers) => {
  try {
    localStorage.setItem(CUSTOMERS_CACHE_KEY, JSON.stringify(customers));
    localStorage.setItem(CACHE_EXPIRY_KEY, (Date.now() + CACHE_DURATION).toString());
  } catch (error) {
    console.error('Error caching customers:', error);
  }
};

/**
 * Get all customers from API with local storage fallback
 * @param {boolean} forceRefresh - Force refresh from API
 * @returns {Promise<Array>} Array of customer objects
 */
export const getCustomers = async (forceRefresh = false) => {
  try {
    // Check cache first if not forcing refresh
    if (!forceRefresh) {
      const cached = getCachedCustomers();
      if (cached) {
        console.log('Using cached customers');
        return cached;
      }
    }

    // Try to fetch from API
    console.log('Fetching customers from API...');
    const response = await customerService.getCustomers({ page: 1, pageSize: 100 });

    if (response && response.result) {
      // Transform backend data to frontend format
      const customers = response.result.map(customer =>
        customerService.transformToFrontendFormat(customer)
      );

      // Always include default walk-in customer
      const defaultCustomers = getDefaultCustomers();
      const allCustomers = [...defaultCustomers, ...customers];

      // Cache the results
      cacheCustomers(allCustomers);

      // Also save to local storage for offline access
      saveCustomers(allCustomers);

      return allCustomers;
    }

    // Fallback to local storage if API fails
    console.log('API failed, falling back to local storage');
    return getCustomersFromLocalStorage();

  } catch (error) {
    console.error('Error fetching customers from API:', error);

    // Fallback to local storage
    return getCustomersFromLocalStorage();
  }
};

/**
 * Get customers from local storage only (original function)
 * @returns {Array} Array of customer objects
 */
export const getCustomersFromLocalStorage = () => {
  try {
    const customers = localStorage.getItem(CUSTOMERS_STORAGE_KEY);
    return customers ? JSON.parse(customers) : getDefaultCustomers();
  } catch (error) {
    console.error('Error retrieving customers from local storage:', error);
    return getDefaultCustomers();
  }
};

/**
 * Get default customers (including Walk-in Customer)
 * @returns {Array} Array of default customer objects
 */
export const getDefaultCustomers = () => {
  return [
    {
      id: 'walk-in',
      name: 'Walk in Customer',
      phone: '',
      email: '',
      address: '',
      isDefault: true,
      createdAt: new Date().toISOString()
    }
  ];
};

/**
 * Save customers to local storage
 * @param {Array} customers - Array of customer objects
 */
export const saveCustomers = (customers) => {
  try {
    localStorage.setItem(CUSTOMERS_STORAGE_KEY, JSON.stringify(customers));
  } catch (error) {
    console.error('Error saving customers to local storage:', error);
  }
};

/**
 * Add a new customer via API with local storage fallback
 * @param {Object} customerData - Customer data object
 * @returns {Promise<Object>} The created customer object
 */
export const addCustomer = async (customerData) => {
  try {
    // Validate required fields
    if (!customerData.name || !customerData.name.trim()) {
      throw new Error('Customer name is required');
    }

    // Try to create via API first
    try {
      console.log('Creating customer via API...');
      const response = await customerService.createCustomer(customerData);

      if (response && response.result) {
        // Get the created customer details
        const createdCustomer = {
          id: response.result.id || generateCustomerId(),
          name: customerData.name.trim(),
          phone: customerData.phone || '',
          email: customerData.email || '',
          address: customerData.address || '',
          city: customerData.city || '',
          country: customerData.country || '',
          isDefault: false,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };

        // Update local storage and cache
        const customers = await getCustomersFromLocalStorage();
        customers.push(createdCustomer);
        saveCustomers(customers);

        // Clear cache to force refresh
        clearCustomersCache();

        return createdCustomer;
      }
    } catch (apiError) {
      console.warn('API creation failed, falling back to local storage:', apiError);
    }

    // Fallback to local storage creation
    const customers = await getCustomersFromLocalStorage();

    // Generate unique ID
    const newCustomer = {
      id: generateCustomerId(),
      name: customerData.name.trim(),
      phone: customerData.phone || '',
      email: customerData.email || '',
      address: customerData.address || '',
      city: customerData.city || '',
      country: customerData.country || '',
      isDefault: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isLocalOnly: true // Flag to indicate this is local-only
    };

    customers.push(newCustomer);
    saveCustomers(customers);

    return newCustomer;
  } catch (error) {
    console.error('Error adding customer:', error);
    throw error;
  }
};

/**
 * Update an existing customer via API with local storage fallback
 * @param {string} customerId - Customer ID
 * @param {Object} customerData - Updated customer data
 * @returns {Promise<Object|null>} Updated customer object or null if not found
 */
export const updateCustomer = async (customerId, customerData) => {
  try {
    if (!customerId) {
      throw new Error('Customer ID is required');
    }

    // Get customers from local storage first
    const customers = await getCustomersFromLocalStorage();
    const existingCustomer = customers.find(c => c.id === customerId);

    if (!existingCustomer) {
      console.warn('Customer not found:', customerId);
      return null;
    }

    // Don't allow updating default customer's core properties
    if (existingCustomer.isDefault) {
      console.warn('Cannot update default customer');
      return existingCustomer;
    }

    // If it's not a local-only customer, try API update
    if (!existingCustomer.isLocalOnly) {
      try {
        console.log('Updating customer via API...');
        const response = await customerService.updateCustomer(customerId, customerData);

        if (response && response.isSuccessful) {
          console.log('Customer updated successfully via API');
        }
      } catch (apiError) {
        console.warn('API update failed, updating locally only:', apiError);
      }
    }

    // Update in local storage regardless of API success
    const customerIndex = customers.findIndex(c => c.id === customerId);

    if (customerIndex === -1) {
      console.warn('Customer not found in local storage:', customerId);
      return null;
    }

    // Update customer data
    customers[customerIndex] = {
      ...customers[customerIndex],
      ...customerData,
      id: customerId, // Ensure ID doesn't change
      updatedAt: new Date().toISOString()
    };

    saveCustomers(customers);

    // Clear cache to force refresh
    clearCustomersCache();

    return customers[customerIndex];
  } catch (error) {
    console.error('Error updating customer:', error);
    throw error;
  }
};

/**
 * Delete a customer
 * @param {string} customerId - Customer ID to delete
 * @returns {boolean} True if deleted successfully
 */
export const deleteCustomer = (customerId) => {
  try {
    const customers = getCustomers();
    const customerIndex = customers.findIndex(customer => customer.id === customerId);
    
    if (customerIndex === -1) {
      console.warn('Customer not found:', customerId);
      return false;
    }

    // Don't allow deleting default customer
    if (customers[customerIndex].isDefault) {
      console.warn('Cannot delete default customer');
      return false;
    }

    customers.splice(customerIndex, 1);
    saveCustomers(customers);
    
    // If this was the selected customer, reset to default
    const selectedCustomer = getSelectedCustomer();
    if (selectedCustomer && selectedCustomer.id === customerId) {
      setSelectedCustomer(customers.find(c => c.isDefault) || customers[0]);
    }
    
    return true;
  } catch (error) {
    console.error('Error deleting customer:', error);
    return false;
  }
};

/**
 * Get customer by ID
 * @param {string} customerId - Customer ID
 * @returns {Object|null} Customer object or null if not found
 */
export const getCustomerById = (customerId) => {
  try {
    const customers = getCustomers();
    return customers.find(customer => customer.id === customerId) || null;
  } catch (error) {
    console.error('Error getting customer by ID:', error);
    return null;
  }
};

/**
 * Get selected customer for current order
 * @returns {Promise<Object|null>} Selected customer object or null
 */
export const getSelectedCustomer = async () => {
  try {
    const selectedCustomer = localStorage.getItem(SELECTED_CUSTOMER_KEY);
    if (selectedCustomer) {
      return JSON.parse(selectedCustomer);
    }

    // Return default customer if none selected
    const customers = await getCustomers();
    return customers.find(c => c.isDefault) || customers[0] || null;
  } catch (error) {
    console.error('Error retrieving selected customer:', error);
    // Fallback to local storage
    const localCustomers = getCustomersFromLocalStorage();
    return localCustomers.find(c => c.isDefault) || localCustomers[0] || null;
  }
};

/**
 * Set selected customer for current order
 * @param {Object} customer - Customer object to select
 */
export const setSelectedCustomer = (customer) => {
  try {
    localStorage.setItem(SELECTED_CUSTOMER_KEY, JSON.stringify(customer));
  } catch (error) {
    console.error('Error setting selected customer:', error);
  }
};

/**
 * Clear selected customer (reset to default)
 */
export const clearSelectedCustomer = () => {
  try {
    localStorage.removeItem(SELECTED_CUSTOMER_KEY);
  } catch (error) {
    console.error('Error clearing selected customer:', error);
  }
};

/**
 * Clear customers cache
 */
export const clearCustomersCache = () => {
  try {
    localStorage.removeItem(CUSTOMERS_CACHE_KEY);
    localStorage.removeItem(CACHE_EXPIRY_KEY);
  } catch (error) {
    console.error('Error clearing customers cache:', error);
  }
};

/**
 * Refresh customers from API
 * @returns {Promise<Array>} Array of customer objects
 */
export const refreshCustomers = async () => {
  try {
    clearCustomersCache();
    return await getCustomers(true);
  } catch (error) {
    console.error('Error refreshing customers:', error);
    return getCustomersFromLocalStorage();
  }
};

/**
 * Generate unique customer ID
 * @returns {string} Unique customer ID
 */
const generateCustomerId = () => {
  return 'cust_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
};

/**
 * Get customers formatted for Select component
 * @param {boolean} useCache - Whether to use cached data
 * @returns {Promise<Array>} Array of options for Select component
 */
export const getCustomersForSelect = async (useCache = true) => {
  try {
    const customers = useCache ? await getCustomers() : await getCustomers(true);
    return customers.map(customer => ({
      value: customer.id,
      label: customer.name,
      customer: customer
    }));
  } catch (error) {
    console.error('Error formatting customers for select:', error);
    // Fallback to local storage
    const localCustomers = getCustomersFromLocalStorage();
    return localCustomers.map(customer => ({
      value: customer.id,
      label: customer.name,
      customer: customer
    }));
  }
};

/**
 * Search customers by name, phone, or email
 * @param {string} searchTerm - Search term
 * @param {boolean} useApi - Whether to use API search
 * @returns {Promise<Array>} Array of matching customers
 */
export const searchCustomers = async (searchTerm, useApi = true) => {
  try {
    if (!searchTerm) return await getCustomers();

    // If API search is enabled and search term is provided, try API first
    if (useApi && searchTerm.trim()) {
      try {
        const response = await customerService.searchCustomers(searchTerm.trim());
        if (response && response.result) {
          const customers = response.result.map(customer =>
            customerService.transformToFrontendFormat(customer)
          );

          // Always include default customers
          const defaultCustomers = getDefaultCustomers();
          return [...defaultCustomers, ...customers];
        }
      } catch (apiError) {
        console.warn('API search failed, falling back to local search:', apiError);
      }
    }

    // Fallback to local search
    const customers = await getCustomers();
    const term = searchTerm.toLowerCase();

    return customers.filter(customer =>
      customer.name.toLowerCase().includes(term) ||
      customer.phone.includes(term) ||
      customer.email.toLowerCase().includes(term)
    );
  } catch (error) {
    console.error('Error searching customers:', error);
    return [];
  }
};

/**
 * Get all customer orders from local storage
 * @returns {Array} Array of customer order objects
 */
export const getCustomerOrders = () => {
  try {
    const orders = localStorage.getItem(CUSTOMER_ORDERS_KEY);
    return orders ? JSON.parse(orders) : [];
  } catch (error) {
    console.error('Error retrieving customer orders from local storage:', error);
    return [];
  }
};

/**
 * Save customer orders to local storage
 * @param {Array} orders - Array of customer order objects
 */
export const saveCustomerOrders = (orders) => {
  try {
    localStorage.setItem(CUSTOMER_ORDERS_KEY, JSON.stringify(orders));
  } catch (error) {
    console.error('Error saving customer orders to local storage:', error);
  }
};

/**
 * Save order for a specific customer
 * @param {Object} orderData - Order data object
 * @returns {Object} The saved order object
 */
export const saveOrderForCustomer = (orderData) => {
  try {
    const customerOrders = getCustomerOrders();

    // Generate unique order ID
    const orderId = generateOrderId();

    const newOrder = {
      id: orderId,
      customerId: orderData.customerId,
      customerName: orderData.customerName,
      orderItems: orderData.orderItems || [],
      orderTotal: orderData.orderTotal || {},
      discountSettings: orderData.discountSettings || {},
      paymentSummary: orderData.paymentSummary || {},
      payments: orderData.payments || [],
      status: orderData.status || 'saved',
      reference: generateOrderReference(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      notes: orderData.notes || ''
    };

    customerOrders.push(newOrder);
    saveCustomerOrders(customerOrders);

    return newOrder;
  } catch (error) {
    console.error('Error saving order for customer:', error);
    throw error;
  }
};

/**
 * Get orders for a specific customer
 * @param {string} customerId - Customer ID
 * @returns {Array} Array of orders for the customer
 */
export const getOrdersForCustomer = (customerId) => {
  try {
    const customerOrders = getCustomerOrders();
    return customerOrders.filter(order => order.customerId === customerId);
  } catch (error) {
    console.error('Error getting orders for customer:', error);
    return [];
  }
};

/**
 * Load a saved order
 * @param {string} orderId - Order ID to load
 * @returns {Object|null} Order object or null if not found
 */
export const loadSavedOrder = (orderId) => {
  try {
    const customerOrders = getCustomerOrders();
    return customerOrders.find(order => order.id === orderId) || null;
  } catch (error) {
    console.error('Error loading saved order:', error);
    return null;
  }
};

/**
 * Update a saved order
 * @param {string} orderId - Order ID to update
 * @param {Object} orderData - Updated order data
 * @returns {Object|null} Updated order object or null if not found
 */
export const updateSavedOrder = (orderId, orderData) => {
  try {
    const customerOrders = getCustomerOrders();
    const orderIndex = customerOrders.findIndex(order => order.id === orderId);

    if (orderIndex === -1) {
      console.warn('Order not found:', orderId);
      return null;
    }

    customerOrders[orderIndex] = {
      ...customerOrders[orderIndex],
      ...orderData,
      id: orderId, // Ensure ID doesn't change
      updatedAt: new Date().toISOString()
    };

    saveCustomerOrders(customerOrders);
    return customerOrders[orderIndex];
  } catch (error) {
    console.error('Error updating saved order:', error);
    throw error;
  }
};

/**
 * Delete a saved order
 * @param {string} orderId - Order ID to delete
 * @returns {boolean} True if deleted successfully
 */
export const deleteSavedOrder = (orderId) => {
  try {
    const customerOrders = getCustomerOrders();
    const orderIndex = customerOrders.findIndex(order => order.id === orderId);

    if (orderIndex === -1) {
      console.warn('Order not found:', orderId);
      return false;
    }

    customerOrders.splice(orderIndex, 1);
    saveCustomerOrders(customerOrders);

    return true;
  } catch (error) {
    console.error('Error deleting saved order:', error);
    return false;
  }
};

/**
 * Complete a saved order (mark as completed)
 * @param {string} orderId - Order ID to complete
 * @param {Object} completionData - Additional completion data
 * @returns {Object|null} Completed order object or null if not found
 */
export const completeSavedOrder = (orderId, completionData = {}) => {
  try {
    const customerOrders = getCustomerOrders();
    const orderIndex = customerOrders.findIndex(order => order.id === orderId);

    if (orderIndex === -1) {
      console.warn('Order not found:', orderId);
      return null;
    }

    customerOrders[orderIndex] = {
      ...customerOrders[orderIndex],
      ...completionData,
      status: 'completed',
      completedAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    saveCustomerOrders(customerOrders);
    return customerOrders[orderIndex];
  } catch (error) {
    console.error('Error completing saved order:', error);
    throw error;
  }
};

/**
 * Generate unique order ID
 * @returns {string} Unique order ID
 */
const generateOrderId = () => {
  return 'order_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
};

/**
 * Generate order reference number
 * @returns {string} Order reference number
 */
const generateOrderReference = () => {
  const timestamp = Date.now().toString().slice(-6);
  const random = Math.random().toString(36).substr(2, 3).toUpperCase();
  return `ORD${timestamp}${random}`;
};
