import React, { useState, useEffect, useCallback } from "react";
import { Link } from "react-router-dom";
import Image<PERSON>ithBasePath from "../../core/img/imagewithbasebath";
import Select from "react-select";
import { Edit, Eye, Trash2, RefreshCw } from "react-feather";
import { useSelector } from "react-redux";
import Table from "../../core/pagination/datatable";
import TooltipIcons from "../../core/common/tooltip-content/tooltipIcons";
import CollapesIcon from "../../core/common/tooltip-content/collapes";
import CommonFooter from "../../core/common/footer/commonFooter";
import CustomerModal from "../../core/modals/peoples/customerModal";
import { getCustomers, refreshCustomers, deleteCustomer } from "../../core/utils/customerStorage";

const Customers = () => {
  // State management
  const [customers, setCustomers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [countryFilter, setCountryFilter] = useState('');

  // Fallback to Redux data if needed
  const reduxData = useSelector((state) => state.rootReducer.customerdata);



  // Load customers from API
  const loadCustomers = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const customersData = await getCustomers();
      setCustomers(customersData || []);
    } catch (err) {
      console.error('Error loading customers:', err);
      setError('Failed to load customers');

      // Fallback to Redux data if available
      if (reduxData && reduxData.length > 0) {
        setCustomers(reduxData);
      }
    } finally {
      setLoading(false);
    }
  }, [reduxData]);

  // Load customers on component mount
  useEffect(() => {
    loadCustomers();
  }, [loadCustomers]);

  // Refresh customers
  const handleRefresh = async () => {
    try {
      setLoading(true);
      const customersData = await refreshCustomers();
      setCustomers(customersData || []);
    } catch (err) {
      console.error('Error refreshing customers:', err);
      setError('Failed to refresh customers');
    } finally {
      setLoading(false);
    }
  };

  // Handle customer deletion
  const handleDeleteCustomer = async (customerId) => {
    if (!customerId) return;

    if (window.confirm('Are you sure you want to delete this customer?')) {
      try {
        const success = await deleteCustomer(customerId);
        if (success) {
          // Reload customers list
          await loadCustomers();
        } else {
          alert('Failed to delete customer');
        }
      } catch (err) {
        console.error('Error deleting customer:', err);
        alert('Error deleting customer: ' + err.message);
      }
    }
  };

  // Handle edit customer
  const handleEditCustomer = (customer) => {
    setSelectedCustomer(customer);
    setIsEditMode(true);
  };

  // Handle customer saved
  const handleCustomerSaved = async (savedCustomer) => {
    console.log('Customer saved:', savedCustomer);
    // Reload customers list
    await loadCustomers();

    // Reset edit mode
    setSelectedCustomer(null);
    setIsEditMode(false);
  };

  // Handle modal close
  const handleModalClose = () => {
    setSelectedCustomer(null);
    setIsEditMode(false);
  };

  // Filter options
  const countries = [
    { label: "All Countries", value: "" },
    { label: "Malaysia", value: "Malaysia" },
    { label: "Singapore", value: "Singapore" },
    { label: "Thailand", value: "Thailand" },
    { label: "Indonesia", value: "Indonesia" },
    { label: "United States", value: "United States" },
    { label: "United Kingdom", value: "United Kingdom" },
    { label: "Germany", value: "Germany" },
    { label: "India", value: "India" },
  ];

  const statusOptions = [
    { label: "All Status", value: "" },
    { label: "Active", value: "Active" },
    { label: "Inactive", value: "Inactive" },
  ];

  // Filter customers based on search and filters
  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = !searchTerm ||
      customer.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.phone?.includes(searchTerm);

    const matchesStatus = !statusFilter || customer.status === statusFilter;
    const matchesCountry = !countryFilter || customer.country === countryFilter;

    return matchesSearch && matchesStatus && matchesCountry;
  });

  // Table columns configuration
  const columns = [
    {
      title: "Customer Name",
      dataIndex: "name",
      key: "name",
      sorter: (a, b) => (a.name || '').localeCompare(b.name || ''),
      render: (text, record) => (
        <div className="d-flex align-items-center">
          <div className="avatar avatar-md me-2">
            <ImageWithBasePath
              src="assets/img/users/user-placeholder.png"
              alt="Customer"
              className="rounded-circle"
            />
          </div>
          <div>
            <h6 className="fw-medium mb-0">{text || 'N/A'}</h6>
            <span className="text-muted small">{record.id}</span>
          </div>
        </div>
      ),
    },
    {
      title: "Email",
      dataIndex: "email",
      key: "email",
      sorter: (a, b) => (a.email || '').localeCompare(b.email || ''),
      render: (text) => text || 'N/A',
    },
    {
      title: "Phone",
      dataIndex: "phone",
      key: "phone",
      sorter: (a, b) => (a.phone || '').localeCompare(b.phone || ''),
      render: (text) => text || 'N/A',
    },
    {
      title: "Country",
      dataIndex: "country",
      key: "country",
      sorter: (a, b) => (a.country || '').localeCompare(b.country || ''),
      render: (text) => text || 'N/A',
    },
    {
      title: "Status",
      dataIndex: "status",
      key: "status",
      sorter: (a, b) => (a.status || '').localeCompare(b.status || ''),
      render: (text) => (
        <span className={`badge ${
          (text || 'Active') === 'Active' ? 'badge-success' : 'badge-danger'
        }`}>
          {text || 'Active'}
        </span>
      ),
    },
    {
      title: "Action",
      key: "action",
      render: (text, record) => (
        <div className="action-table-data">
          <div className="edit-delete-action">
            <Link
              className="me-2 p-2"
              to="#"
              title="View Customer"
            >
              <Eye className="feather-view" />
            </Link>

            <Link
              className="me-2 p-2"
              to="#"
              data-bs-toggle="modal"
              data-bs-target="#edit-units"
              onClick={() => handleEditCustomer(record)}
              title="Edit Customer"
            >
              <Edit className="feather-edit" />
            </Link>

            {!record.isDefault && (
              <Link
                className="confirm-text p-2"
                to="#"
                onClick={() => handleDeleteCustomer(record.id)}
                title="Delete Customer"
              >
                <Trash2 className="feather-trash-2" />
              </Link>
            )}
          </div>
        </div>
      ),
    },
  ];

  return (
    <>
      <div className="page-wrapper">
        <div className="content">
          <div className="page-header">
            <div className="add-item d-flex">
              <div className="page-title">
                <h4 className="fw-bold">Customers</h4>
                <h6>Manage your customers</h6>
              </div>
            </div>
            <ul className="table-top-head">
              <TooltipIcons />
              <li>
                <button
                  className="btn btn-outline-primary btn-sm"
                  onClick={handleRefresh}
                  disabled={loading}
                  title="Refresh Customers"
                >
                  <RefreshCw className={`feather-refresh ${loading ? 'spin' : ''}`} />
                </button>
              </li>
              <CollapesIcon />
            </ul>
            <div className="page-btn">
              <Link
                to="#"
                className="btn btn-primary text-white"
                data-bs-toggle="modal"
                data-bs-target="#add-units"
                onClick={() => {
                  setSelectedCustomer(null);
                  setIsEditMode(false);
                }}
              >
                <i className='ti ti-circle-plus me-1'></i>
                Add Customer
              </Link>
            </div>
          </div>
          {/* Customer list */}
          <div className="card table-list-card">
            <div className="card-header d-flex align-items-center justify-content-between flex-wrap row-gap-3">
              <div className="search-set">
                <div className="search-input">
                  <input
                    type="text"
                    placeholder="Search customers..."
                    className="form-control"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                  <i className="fas fa-search"></i>
                </div>
              </div>
              <div className="d-flex table-dropdown my-xl-auto right-content align-items-center flex-wrap row-gap-3">
                <div className="me-2">
                  <Select
                    classNamePrefix="react-select"
                    options={statusOptions}
                    value={statusOptions.find(option => option.value === statusFilter)}
                    onChange={(option) => setStatusFilter(option ? option.value : '')}
                    placeholder="Filter by Status"
                    isClearable
                  />
                </div>
                <div className="me-2">
                  <Select
                    classNamePrefix="react-select"
                    options={countries}
                    value={countries.find(option => option.value === countryFilter)}
                    onChange={(option) => setCountryFilter(option ? option.value : '')}
                    placeholder="Filter by Country"
                    isClearable
                  />
                </div>
              </div>
            </div>
            <div className="card-body">
              {/* Loading State */}
              {loading && (
                <div className="text-center py-4">
                  <div className="spinner-border text-primary" role="status">
                    <span className="visually-hidden">Loading...</span>
                  </div>
                  <p className="mt-2">Loading customers...</p>
                </div>
              )}

              {/* Error State */}
              {error && !loading && (
                <div className="alert alert-danger" role="alert">
                  <i className="fas fa-exclamation-triangle me-2"></i>
                  {error}
                  <button
                    className="btn btn-sm btn-outline-danger ms-2"
                    onClick={handleRefresh}
                  >
                    Retry
                  </button>
                </div>
              )}

              {/* Empty State */}
              {!loading && !error && filteredCustomers.length === 0 && (
                <div className="text-center py-4">
                  <i className="fas fa-users fa-3x text-muted mb-3"></i>
                  <h5>No customers found</h5>
                  <p className="text-muted">
                    {searchTerm || statusFilter || countryFilter
                      ? 'Try adjusting your search or filters'
                      : 'Start by adding your first customer'
                    }
                  </p>
                  {!searchTerm && !statusFilter && !countryFilter && (
                    <Link
                      to="#"
                      className="btn btn-primary"
                      data-bs-toggle="modal"
                      data-bs-target="#add-units"
                    >
                      <i className='ti ti-circle-plus me-1'></i>
                      Add Customer
                    </Link>
                  )}
                </div>
              )}

              {/* Table */}
              {!loading && !error && filteredCustomers.length > 0 && (
                <div className="table-responsive">
                  <Table
                    columns={columns}
                    dataSource={filteredCustomers}
                    rowKey="id"
                    pagination={{
                      pageSize: 10,
                      showSizeChanger: true,
                      showQuickJumper: true,
                      showTotal: (total, range) =>
                        `Showing ${range[0]}-${range[1]} of ${total} customers`,
                    }}
                  />
                </div>
              )}
            </div>
          </div>
          {/* /Customer list */}
        </div>
        <CommonFooter />
      </div>

      {/* Enhanced Customer Modal */}
      <CustomerModal
        isEdit={isEditMode}
        customerData={selectedCustomer}
        onCustomerSaved={handleCustomerSaved}
        onClose={handleModalClose}
      />
    </>
  );
};

export default Customers;
