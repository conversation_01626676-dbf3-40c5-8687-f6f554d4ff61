/**
 * Customer Service
 * Handles API calls related to customers
 */

import { handleApiResponse } from '../utils/api.utils';
import { apiGet, apiPost, apiPut } from '../utils/api.interceptor';

/**
 * Service for handling customer-related API operations
 */
class CustomerService {
  /**
   * Get all customers with pagination and filtering
   * @param {Object} pagination - Pagination parameters
   * @param {Object} commonFilter - Common filter parameters
   * @param {Object} options - Additional fetch options
   * @returns {Promise} - Promise with customers data
   */
  async getCustomers(pagination = {}, commonFilter = {}, options = {}) {
    try {
      // Build query parameters
      const queryParams = new URLSearchParams();
      
      // Add pagination parameters
      if (pagination.page) queryParams.append('page', pagination.page);
      if (pagination.pageSize) queryParams.append('pageSize', pagination.pageSize);
      if (pagination.sortBy) queryParams.append('sortBy', pagination.sortBy);
      if (pagination.sortOrder) queryParams.append('sortOrder', pagination.sortOrder);
      
      // Add common filter parameters
      if (commonFilter.search) queryParams.append('search', commonFilter.search);
      if (commonFilter.status) queryParams.append('status', commonFilter.status);
      if (commonFilter.country) queryParams.append('country', commonFilter.country);
      
      const queryString = queryParams.toString();
      const url = queryString ? `/customer?${queryString}` : '/customer';
      
      const response = await apiGet(url, options);
      return handleApiResponse(response);
    } catch (error) {
      console.error('Error fetching customers:', error);
      throw error;
    }
  }

  /**
   * Get customer by ID
   * @param {string} customerId - Customer ID (GUID)
   * @param {Object} options - Additional fetch options
   * @returns {Promise} - Promise with customer data
   */
  async getCustomerById(customerId, options = {}) {
    try {
      if (!customerId) {
        throw new Error('Customer ID is required');
      }

      const response = await apiGet(`/customer/Get/${customerId}`, options);
      return handleApiResponse(response);
    } catch (error) {
      console.error('Error fetching customer by ID:', error);
      throw error;
    }
  }

  /**
   * Create a new customer
   * @param {Object} customerData - Customer data object
   * @param {Object} options - Additional fetch options
   * @returns {Promise} - Promise with creation response
   */
  async createCustomer(customerData, options = {}) {
    try {
      // Validate required fields
      if (!customerData.name) {
        throw new Error('Customer name is required');
      }

      // Transform frontend data to backend format
      const customerRequest = this.transformToBackendFormat(customerData);

      const response = await apiPost('/customer/Create', customerRequest, options);
      return handleApiResponse(response);
    } catch (error) {
      console.error('Error creating customer:', error);
      throw error;
    }
  }

  /**
   * Update an existing customer
   * @param {string} customerId - Customer ID (GUID)
   * @param {Object} customerData - Updated customer data
   * @param {Object} options - Additional fetch options
   * @returns {Promise} - Promise with update response
   */
  async updateCustomer(customerId, customerData, options = {}) {
    try {
      if (!customerId) {
        throw new Error('Customer ID is required');
      }

      // Transform frontend data to backend format
      const updateRequest = this.transformToBackendFormat(customerData, true);

      const response = await apiPut(`/customer/Update/${customerId}`, updateRequest, options);
      return handleApiResponse(response);
    } catch (error) {
      console.error('Error updating customer:', error);
      throw error;
    }
  }

  /**
   * Delete a customer
   * @param {string} customerId - Customer ID (GUID)
   * @param {Object} options - Additional fetch options
   * @returns {Promise} - Promise with deletion response
   */
  async deleteCustomer(customerId, options = {}) {
    try {
      if (!customerId) {
        throw new Error('Customer ID is required');
      }

      const response = await apiPost('/customer/Delete', customerId, options);
      return handleApiResponse(response);
    } catch (error) {
      console.error('Error deleting customer:', error);
      throw error;
    }
  }

  /**
   * Get terms list with pagination
   * @param {Object} pagination - Pagination parameters
   * @param {Object} options - Additional fetch options
   * @returns {Promise} - Promise with terms data
   */
  async getTerms(pagination = {}, options = {}) {
    try {
      const queryParams = new URLSearchParams();
      
      if (pagination.page) queryParams.append('page', pagination.page);
      if (pagination.pageSize) queryParams.append('pageSize', pagination.pageSize);
      
      const queryString = queryParams.toString();
      const url = queryString ? `/customer/Terms?${queryString}` : '/customer/Terms';
      
      const response = await apiGet(url, options);
      return handleApiResponse(response);
    } catch (error) {
      console.error('Error fetching terms:', error);
      throw error;
    }
  }

  /**
   * Get term by ID
   * @param {string} termId - Term ID (GUID)
   * @param {Object} options - Additional fetch options
   * @returns {Promise} - Promise with term data
   */
  async getTermById(termId, options = {}) {
    try {
      if (!termId) {
        throw new Error('Term ID is required');
      }

      const response = await apiGet(`/customer/Term/${termId}`, options);
      return handleApiResponse(response);
    } catch (error) {
      console.error('Error fetching term by ID:', error);
      throw error;
    }
  }

  /**
   * Get address by ID
   * @param {string} addressId - Address ID (GUID)
   * @param {Object} options - Additional fetch options
   * @returns {Promise} - Promise with address data
   */
  async getAddressById(addressId, options = {}) {
    try {
      if (!addressId) {
        throw new Error('Address ID is required');
      }

      const response = await apiGet(`/customer/Address/${addressId}`, options);
      return handleApiResponse(response);
    } catch (error) {
      console.error('Error fetching address by ID:', error);
      throw error;
    }
  }

  /**
   * Transform frontend customer data to backend format
   * @param {Object} customerData - Frontend customer data
   * @param {boolean} isUpdate - Whether this is for update operation
   * @returns {Object} - Backend formatted customer data
   */
  transformToBackendFormat(customerData, isUpdate = false) {
    const baseData = {
      code: customerData.code || '',
      name: customerData.name || '',
      description: customerData.description || '',
      isTaxExempt: customerData.isTaxExempt || false,
      isPICEditable: customerData.isPICEditable || true,
      defaultPIC: customerData.defaultPIC || '',
      accountCode: customerData.accountCode || '',
      remark: customerData.remark || '',
      identityTypeId: customerData.identityTypeId || null,
      identityNo: customerData.identityNo || '',
      fullName: customerData.fullName || customerData.name || '',
      email: customerData.email || '',
      tinNo: customerData.tinNo || '',
      tinVerifyStatus: customerData.tinVerifyStatus || 0,
      debtorTypeId: customerData.debtorTypeId || null,
      referrerId: customerData.referrerId || null,
      currencyId: customerData.currencyId || null,
      accountGroupId: customerData.accountGroupId || null,
      companyId: customerData.companyId || null
    };

    // Add address if provided
    if (customerData.address || customerData.phone || customerData.firstName || customerData.lastName) {
      baseData.address = {
        firstName: customerData.firstName || '',
        lastName: customerData.lastName || '',
        address1: customerData.address1 || customerData.address || '',
        address2: customerData.address2 || '',
        address3: customerData.address3 || '',
        postalCode: customerData.postalCode || '',
        phoneNo: customerData.phone || customerData.phoneNo || '',
        faxNo: customerData.faxNo || '',
        coordinate: customerData.coordinate || '',
        countryId: customerData.countryId || null,
        stateProvinceId: customerData.stateProvinceId || null,
        regionId: customerData.regionId || null
      };
    }

    // For update operations, include ID if available
    if (isUpdate && customerData.id) {
      baseData.id = customerData.id;
    }

    return baseData;
  }

  /**
   * Transform backend customer data to frontend format
   * @param {Object} backendData - Backend customer data
   * @returns {Object} - Frontend formatted customer data
   */
  transformToFrontendFormat(backendData) {
    if (!backendData) return null;

    return {
      id: backendData.id,
      code: backendData.code || '',
      name: backendData.name || '',
      description: backendData.description || '',
      isTaxExempt: backendData.isTaxExempt || false,
      isPICEditable: backendData.isPICEditable || true,
      defaultPIC: backendData.defaultPIC || '',
      accountCode: backendData.accountCode || '',
      remark: backendData.remark || '',
      identityTypeId: backendData.identityTypeId || null,
      identityNo: backendData.identityNo || '',
      fullName: backendData.fullName || '',
      email: backendData.email || '',
      tinNo: backendData.tinNo || '',
      tinVerifyStatus: backendData.tinVerifyStatus || 0,
      debtorTypeId: backendData.debtorTypeId || null,
      referrerId: backendData.referrerId || null,
      currencyId: backendData.currencyId || null,
      accountGroupId: backendData.accountGroupId || null,
      companyId: backendData.companyId || null,
      // Address fields (flattened for easier form handling)
      firstName: backendData.address?.firstName || '',
      lastName: backendData.address?.lastName || '',
      address1: backendData.address?.address1 || '',
      address2: backendData.address?.address2 || '',
      address3: backendData.address?.address3 || '',
      postalCode: backendData.address?.postalCode || '',
      phone: backendData.address?.phoneNo || '',
      phoneNo: backendData.address?.phoneNo || '',
      faxNo: backendData.address?.faxNo || '',
      coordinate: backendData.address?.coordinate || '',
      countryId: backendData.address?.countryId || null,
      stateProvinceId: backendData.address?.stateProvinceId || null,
      regionId: backendData.address?.regionId || null,
      // Legacy fields for backward compatibility
      address: backendData.address?.address1 || '',
      city: backendData.address?.city || '',
      country: backendData.address?.country || '',
      status: backendData.status || 'Active',
      createdAt: backendData.createdAt || new Date().toISOString(),
      updatedAt: backendData.updatedAt || new Date().toISOString(),
      isDefault: false
    };
  }

  /**
   * Search customers by name, phone, or email
   * @param {string} searchTerm - Search term
   * @param {Object} options - Additional fetch options
   * @returns {Promise} - Promise with search results
   */
  async searchCustomers(searchTerm, options = {}) {
    try {
      const pagination = { page: 1, pageSize: 50 };
      const commonFilter = { search: searchTerm };
      
      return await this.getCustomers(pagination, commonFilter, options);
    } catch (error) {
      console.error('Error searching customers:', error);
      throw error;
    }
  }
}

// Create and export a singleton instance
const customerService = new CustomerService();
export default customerService;
